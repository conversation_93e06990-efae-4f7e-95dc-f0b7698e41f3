schemaVersion: "0.3"
description: |-
  MINIMAL TEST VERSION - Configuration Loading Test
  This is a minimal test version to isolate the LinkedHashMap casting error.
  Tests basic PowerShell function execution without S3/JSON operations.
assumeRole: "{{AutomationAssumeRole}}"
parameters:
  AutomationAssumeRole:
    default: ""
    description: (Optional) The ARN of the role that allows Automation to perform the actions on your behalf.
    type: String
  AssetOwner:
    description: (Required) Asset Owner (e.g., Sanlam, Santam, Retail Mass)
    type: String
  AppType:
    description: (Required) Application Type (e.g., Shared, MSSQL)
    type: String
  Client:
    description: (Required) Client identifier (e.g., SGT, SPF, SC, STM, etc.)
    type: String
  Environment:
    description: (Required) Environment (e.g., PRD, PPE, DEV)
    type: String
  OSVersion:
    description: (Required) OS Version (e.g., Windows Server 2022, Windows Server 2019)
    type: String
  S3ConfigBucket:
    description: (Required) S3 bucket containing configuration files
    type: String
    default: sgt-imagebuilder
  Region:
    description: (Optional) AWS Region
    type: String
    default: af-south-1
mainSteps:
  - description: MINIMAL TEST - Basic PowerShell function execution
    name: minimalTest
    action: aws:executeScript
    timeoutSeconds: 300
    isEnd: true
    inputs:
      Runtime: PowerShell 7.4
      Handler: minimalTestFunction
      Script: |-
        function minimalTestFunction {
          param($AssetOwner, $AppType, $Client, $Environment, $OSVersion, $S3ConfigBucket, $Region)
          
          try {
            Write-Output "=== MINIMAL TEST - Configuration Loading Started ==="
            Write-Output "Asset Owner: $AssetOwner"
            Write-Output "App Type: $AppType"
            Write-Output "Client: $Client"
            Write-Output "Environment: $Environment"
            Write-Output "OS Version: $OSVersion"
            Write-Output "S3 Config Bucket: $S3ConfigBucket"
            Write-Output "Region: $Region"
            Write-Output "=== MINIMAL TEST - Skipping all JSON operations ==="
            
            # MINIMAL TEST - Skip all S3 and JSON operations that might create objects
            Write-Output "=== MINIMAL TEST - Returning simple success without any object creation ==="
            "MINIMAL_TEST_SUCCESS"
            
          } catch {
            Write-Error "Minimal test failed: $($_.Exception.Message)"
            throw "Minimal test failed: $($_.Exception.Message)"
          }
        }
      InputPayload:
        AssetOwner: "{{AssetOwner}}"
        AppType: "{{AppType}}"
        Client: "{{Client}}"
        Environment: "{{Environment}}"
        OSVersion: "{{OSVersion}}"
        S3ConfigBucket: "{{S3ConfigBucket}}"
        Region: "{{Region}}"
    outputs:
      - Name: TestResult
        Selector: $.Payload
        Type: String
