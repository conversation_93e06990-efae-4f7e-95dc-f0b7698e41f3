schemaVersion: "0.3"
description: |-
  Configuration Loading and AD Object Creation Validation Runbook
  This runbook loads deployment configuration from S3, creates AD computer objects via webhook API,
  and validates the results. Useful for testing configuration and AD integration before full deployment.
assumeRole: "{{AutomationAssumeRole}}"
parameters:
  AutomationAssumeRole:
    default: ""
    description: (Optional) The ARN of the role that allows Automation to perform the actions on your behalf.
    type: String
  AssetOwner:
    description: (Required) Asset Owner (e.g., Sanlam, Santam, Retail Mass)
    type: String
  AppType:
    description: (Required) Application Type (e.g., Shared, MSSQL)
    type: String
  Client:
    description: (Required) Client identifier (e.g., SGT, SPF, SC, STM, etc.)
    type: String
  Environment:
    description: (Required) Environment (e.g., PRD, PPE, DEV)
    type: String
  OSVersion:
    description: (Required) OS Version (e.g., Windows Server 2022, Windows Server 2019)
    type: String
  S3ConfigBucket:
    description: (Required) S3 bucket containing configuration files
    type: String
    default: sgt-imagebuilder
  SecretsManagerSecretArn:
    description: (Required) Secrets Manager secret ARN containing domain credentials and webhook API details
    type: String
  ComputerName:
    description: (Optional) Custom computer name. If not provided, will be auto-generated
    type: String
    default: ""
  Region:
    description: (Optional) AWS Region
    type: String
    default: af-south-1
  TestMode:
    description: (Optional) Run in test mode - validates configuration and webhook connectivity without creating actual AD objects
    type: String
    default: "false"
    allowedValues:
      - "true"
      - "false"
  CloudWatchLogGroup:
    description: (Optional) CloudWatch Log Group name for detailed logging
    type: String
    default: "/aws/ssm/automation/create-ad-object"
mainSteps:
  - description: Load and validate deployment configuration from S3
    name: loadConfiguration
    action: aws:executeScript
    timeoutSeconds: 300
    nextStep: createADObject
    isEnd: false
    inputs:
      Runtime: PowerShell 7.4
      Script: |-
        # Convert to inline script to avoid PowerShell function Handler issues
        try {
          # Setup CloudWatch logging (without functions to avoid LinkedHashMap issues)
          $logGroupName = $env:CloudWatchLogGroup
          $logStreamName = "loadConfiguration-$(Get-Date -Format 'yyyyMMdd-HHmmss')"

          # Direct logging without functions
          Write-Output "=== Configuration Loading Started ==="
          try {
            if ($logGroupName -and $logGroupName -ne "") {
              Write-CWLEvent -LogGroupName $logGroupName -LogStreamName $logStreamName -LogEvent @{
                Message = "=== Configuration Loading Started ==="
                Timestamp = [DateTimeOffset]::UtcNow.ToUnixTimeMilliseconds()
              }
            }
          } catch { Write-Output "CloudWatch logging failed: $($_.Exception.Message)" }

          Write-Output "Asset Owner: $env:AssetOwner"
          try {
            if ($logGroupName -and $logGroupName -ne "") {
              Write-CWLEvent -LogGroupName $logGroupName -LogStreamName $logStreamName -LogEvent @{
                Message = "Asset Owner: $env:AssetOwner"
                Timestamp = [DateTimeOffset]::UtcNow.ToUnixTimeMilliseconds()
              }
            }
          } catch { Write-Output "CloudWatch logging failed: $($_.Exception.Message)" }

          Write-Output "App Type: $env:AppType"
          Write-Output "Client: $env:Client"
          Write-Output "Environment: $env:Environment"
          Write-Output "OS Version: $env:OSVersion"
          Write-Output "S3 Config Bucket: $env:S3ConfigBucket"
          Write-Output "Region: $env:Region"

          # Load base configuration
          $baseConfigKey = "windows/configs/base_config.json"
          $tempFile = [System.IO.Path]::GetTempFileName()

          Write-LogMessage "Downloading base configuration from: s3://$env:S3ConfigBucket/$baseConfigKey" $logGroupName $logStreamName

          # Use PowerShell AWS modules instead of AWS CLI
          try {
            $identity = Get-STSCallerIdentity -Region $env:Region
            Write-LogMessage "Current AWS identity: $(ConvertTo-Json $identity -Compress)" $logGroupName $logStreamName
            Write-LogMessage "Account: $($identity.Account)" $logGroupName $logStreamName
            Write-LogMessage "User/Role ARN: $($identity.Arn)" $logGroupName $logStreamName
            Write-LogMessage "User ID: $($identity.UserId)" $logGroupName $logStreamName

            # Test S3 bucket access first
            Write-Output "Testing S3 bucket access..."
            try {
              $bucketLocation = Get-S3BucketLocation -BucketName $env:S3ConfigBucket
              Write-Output "S3 bucket location: $bucketLocation"
            } catch {
              Write-Output "Cannot get bucket location: $($_.Exception.Message)"
            }

            # Try to list objects in the bucket
            try {
              $bucketObjects = Get-S3Object -BucketName $env:S3ConfigBucket -Prefix "windows/configs/" -Region $env:Region -MaxKeys 10
              Write-Output "Found $($bucketObjects.Count) objects in windows/configs/ prefix"
              foreach ($obj in $bucketObjects) {
                Write-Output "  - $($obj.Key) (Size: $($obj.Size) bytes)"
              }
            } catch {
              Write-Output "Cannot list bucket objects: $($_.Exception.Message)"
            }

            # Download file from S3 using PowerShell
            Write-Output "Attempting to download: $baseConfigKey"
            Read-S3Object -BucketName $env:S3ConfigBucket -Key $baseConfigKey -File $tempFile -Region $env:Region
            Write-Output "S3 download completed successfully"
          } catch {
            Write-Output "S3 operation failed: $($_.Exception.Message)"
            Write-Output "Exception type: $($_.Exception.GetType().FullName)"
            if ($_.Exception.InnerException) {
              Write-Output "Inner exception: $($_.Exception.InnerException.Message)"
            }
            throw "Failed to download base config from S3. Check IAM permissions for S3 bucket access."
          }

          $baseConfig = Get-Content $tempFile -Raw | ConvertFrom-Json
          Remove-Item $tempFile -Force
          Write-Output "Base configuration loaded successfully"

          # Return simple success for now to test S3 access
          "CONFIG_LOADED_SUCCESS"

        } catch {
          Write-Error "Configuration loading failed: $($_.Exception.Message)"
          throw "Configuration loading failed: $($_.Exception.Message)"
        }
      InputPayload:
        AssetOwner: "{{AssetOwner}}"
        AppType: "{{AppType}}"
        Client: "{{Client}}"
        Environment: "{{Environment}}"
        OSVersion: "{{OSVersion}}"
        S3ConfigBucket: "{{S3ConfigBucket}}"
        Region: "{{Region}}"
        CloudWatchLogGroup: "{{CloudWatchLogGroup}}"
    outputs:
      - Name: ConfigurationResult
        Selector: $.Payload
        Type: String
  - description: Create AD computer object via on-premises webhook API with enhanced logging
    name: createADObject
    action: aws:executeScript
    timeoutSeconds: 600
    nextStep: validateResults
    isEnd: false
    inputs:
      Runtime: PowerShell 7.4
      Script: |-
        # Convert to inline script to avoid PowerShell function Handler issues
        try {
          Write-Output "=== AD Object Creation Started ==="

          # Load System.Web for URL encoding
          Add-Type -AssemblyName System.Web
          $config = $env:ConfigJson | ConvertFrom-Json

          Write-Output "Configuration loaded:"
          Write-Output "  Asset Owner: $($config.AssetOwner)"
          Write-Output "  Client: $($config.Client)"
          Write-Output "  App Type: $($config.AppType)"
          Write-Output "  Environment: $($config.Environment)"
          Write-Output "  Domain: $($config.Domain)"
          Write-Output "  Target OU: $($config.TargetOU)"
          Write-Output "  Test Mode: $env:TestMode"

          # Get webhook credentials from Secrets Manager
          Write-Output "Retrieving credentials from Secrets Manager..."
          Write-Output "Secret ARN: $env:SecretsManagerSecretArn"

          # Use PowerShell modules instead of AWS CLI
          try {
            $identity = Get-STSCallerIdentity -Region $env:Region
            Write-Output "Current AWS identity: $(ConvertTo-Json $identity -Compress)"

            $secretValue = Get-SECSecretValue -SecretId $env:SecretsManagerSecretArn -Region $env:Region
            $secretJson = $secretValue.SecretString | ConvertFrom-Json
            Write-Output "Secrets Manager credentials retrieved successfully"
          } catch {
            Write-Output "Secrets Manager access failed with error: $($_.Exception.Message)"
            Write-Output "Error details: $($_.Exception.GetType().FullName)"
            Write-Output "Checking Secrets Manager permissions..."
            try {
              $secretDesc = Get-SECSecret -SecretId $env:SecretsManagerSecretArn -Region $env:Region
              Write-Output "Secret exists and is accessible: $($secretDesc.Name)"
            } catch {
              Write-Output "Secret not accessible via PowerShell either: $($_.Exception.Message)"
            }
            throw "Failed to retrieve credentials from Secrets Manager. Check IAM permissions for Secrets Manager access."
          }

          # Determine computer name
          $computerName = if ($env:ComputerName -and $env:ComputerName -ne "") {
            $env:ComputerName
          } else {
            "$($config.AssetOwner)-$($config.Client)-$($config.AppType)-$(Get-Date -Format 'yyyyMMdd-HHmm')"
          }
          Write-Output "Computer name determined: $computerName"

          # Generate job ID for tracking
          $jobId = "AWS-SSM-$(Get-Date -Format 'yyyyMMdd-HHmmss')"
          Write-Output "Job ID generated: $jobId"

          # Create description
          $description = "AWS EC2 - $($config.AssetOwner) $($config.Client) $($config.AppType) $($config.Environment)"
          Write-Output "Description: $description"

          # Get webhook configuration from secrets
          $apiKey = $secretJson.webhookApiKey
          $webhookBaseUrl = $secretJson.webhookApiUrl
          $scriptName = "Create-ADObject.ps1"

          # Validate webhook configuration
          if (-not $apiKey -or -not $webhookBaseUrl) {
            throw "Webhook API key or URL not found in Secrets Manager. Please ensure 'webhookApiKey' and 'webhookApiUrl' are configured."
          }
          Write-Output "Webhook configuration validated"
          Write-Output "  Webhook URL: $webhookBaseUrl"
          Write-Output "  Script: $scriptName"

          # URL encode the parameters
          $paramString = "-jobId '$jobId' -objectName '$computerName' -objectDescription '$description' -vmOS 'Windows' -domain '$($config.Domain)' -ouPath '$($config.TargetOU)' -appType '$($config.AppType)'"
          $encodedParams = [System.Web.HttpUtility]::UrlEncode($paramString)

          # Build the webhook URL
          $webhookUrl = "$webhookBaseUrl?key=$apiKey&script=$scriptName&param=$encodedParams"

          Write-Output "=== Webhook API Call Details ==="
          Write-Output "Target Computer: $computerName"
          Write-Output "Target OU: $($config.TargetOU)"
          Write-Output "Domain: $($config.Domain)"
          Write-Output "Job ID: $jobId"
          Write-Output "Parameters: $paramString"

          if ($env:TestMode -eq 'true') {
            Write-Output "=== TEST MODE - Simulating API Call ==="
            Write-Output "Would call: $webhookBaseUrl"
            Write-Output "With parameters: $paramString"

            # Simulate successful response for testing
            $action = "TEST_MODE_SIMULATION"
            $apiSuccess = $true
            $apiMessage = "Test mode - API call simulated successfully"
            $actualOU = $config.TargetOU

            Write-Output "Test mode simulation completed"
          } else {
            # Call the webhook API
            Write-Output "Calling webhook API..."
            try {
              $response = Invoke-RestMethod -Uri $webhookUrl -Method GET -TimeoutSec 120
              Write-Output "Webhook API call completed"
              Write-Output "Raw webhook response: $($response | ConvertTo-Json -Depth 10)"

              # Parse the response structure
              if ($response.output) {
                $apiResult = $response.output

                if ($apiResult.success -eq $true -and $apiResult.status -eq "COMPLETED") {
                  Write-Output "=== AD Object Creation Successful ==="
                  Write-Output "  Computer Name: $($apiResult.data.objectName)"
                  Write-Output "  Domain: $($apiResult.data.Domain)"
                  Write-Output "  OU Created In: $($apiResult.data.ouCreatedIn)"
                  Write-Output "  Timestamp: $($apiResult.data.timeStamp)"
                  Write-Output "  Message: $($apiResult.message)"
                  $action = "CREATED_VIA_API"
                  $apiSuccess = $true
                  $apiMessage = $apiResult.message
                  $actualOU = $apiResult.data.ouCreatedIn
                } else {
                  Write-Warning "=== AD Object Creation Failed ==="
                  Write-Warning "  Status: $($apiResult.status)"
                  Write-Warning "  Message: $($apiResult.message)"
                  Write-Output "Using default OU for fallback"

                  # Get default OU from secrets manager or use fallback
                  $defaultOU = if ($secretJson.defaultTargetOU) {
                    $secretJson.defaultTargetOU
                  } else {
                    $config.TargetOU
                  }

                  Write-Output "Default OU for fallback: $defaultOU"
                  $action = "API_FAILED_USING_DEFAULT"
                  $apiSuccess = $false
                  $apiMessage = $apiResult.message
                  $actualOU = $defaultOU
                }
              } else {
                Write-Warning "=== Unexpected Response Format ==="
                Write-Output "Using default OU for fallback"

                # Get default OU from secrets manager or use fallback
                $defaultOU = if ($secretJson.defaultTargetOU) {
                  $secretJson.defaultTargetOU
                } else {
                  $config.TargetOU
                }

                Write-Output "Default OU for fallback: $defaultOU"
                $action = "API_FAILED_USING_DEFAULT"
                $apiSuccess = $false
                $apiMessage = "Unexpected response format"
                $actualOU = $defaultOU
              }
            } catch {
              # If webhook fails, use default OU for domain join
              Write-Warning "=== Webhook API Call Failed ==="
              Write-Warning "Error: $($_.Exception.Message)"
              Write-Output "Using default OU for fallback"

              # Get default OU from secrets manager or use fallback
              $defaultOU = if ($secretJson.defaultTargetOU) {
                $secretJson.defaultTargetOU
              } else {
                $config.TargetOU
              }

              Write-Output "Default OU for fallback: $defaultOU"
              $action = "API_FAILED_USING_DEFAULT"
              $apiSuccess = $false
              $apiMessage = $_.Exception.Message
              $actualOU = $defaultOU
            }
          }

          # Create result as simple string to avoid object issues
          $timestamp = if ($apiResult.data.timeStamp) { $apiResult.data.timeStamp } else { Get-Date -Format "yyyy-MM-dd HH:mm:ss" }
          $resultString = "$computerName|$action|$($config.TargetOU)|$actualOU|$($config.Domain)|$jobId|$webhookBaseUrl|$description|$apiSuccess|$apiMessage|$env:TestMode|$timestamp|$($config.AssetOwner)|$($config.Client)|$($config.AppType)|$($config.Environment)|$($config.OSVersion)"

          Write-Output "=== AD Object Creation Completed ==="
          Write-Output "Result string: $resultString"

          # Return simple string to avoid LinkedHashMap issues
          $resultString

        } catch {
          Write-Error "AD object creation failed: $($_.Exception.Message)"
          throw "AD object creation failed: $($_.Exception.Message)"
        }
      InputPayload:
        ConfigJson: "{{loadConfiguration.ConfigurationResult}}"
        ComputerName: "{{ComputerName}}"
        Region: "{{Region}}"
        SecretsManagerSecretArn: "{{SecretsManagerSecretArn}}"
        TestMode: "{{TestMode}}"
    outputs:
      - Name: ADObjectResult
        Selector: $.Payload
        Type: String
  - description: Validate configuration loading and AD object creation results
    name: validateResults
    action: aws:executeScript
    timeoutSeconds: 300
    isEnd: true
    inputs:
      Runtime: PowerShell 7.4
      Script: |-
        # Convert to inline script to avoid PowerShell function Handler issues
        try {
          Write-Output "=== Results Validation Started ==="
          Write-Output "ConfigJson type: $($env:ConfigJson.GetType().FullName)"
          Write-Output "ADObjectJson type: $($env:ADObjectJson.GetType().FullName)"

          # Parse the simple string results from previous steps
          # ConfigJson should be a simple string like "CONFIG_LOADED_SUCCESS"
          # ADObjectJson should be a pipe-delimited string with result data

          Write-Output "Config result: $env:ConfigJson"
          Write-Output "AD Object result: $env:ADObjectJson"

          # Parse the AD Object result string (pipe-delimited)
          # Format: computerName|action|targetOU|actualOU|domain|jobId|webhookUrl|description|apiSuccess|apiMessage|testMode|timestamp|assetOwner|client|appType|environment|osVersion
          $adResultParts = $env:ADObjectJson -split '\|'

          if ($adResultParts.Count -ge 17) {
            $computerName = $adResultParts[0]
            $action = $adResultParts[1]
            $targetOU = $adResultParts[2]
            $actualOU = $adResultParts[3]
            $domain = $adResultParts[4]
            $jobId = $adResultParts[5]
            $webhookUrl = $adResultParts[6]
            $description = $adResultParts[7]
            $apiSuccess = $adResultParts[8] -eq 'True'
            $apiMessage = $adResultParts[9]
            $testMode = $adResultParts[10]
            $timestamp = $adResultParts[11]
            $assetOwner = $adResultParts[12]
            $client = $adResultParts[13]
            $appType = $adResultParts[14]
            $environment = $adResultParts[15]
            $osVersion = $adResultParts[16]
          } else {
            Write-Warning "AD Object result string format unexpected. Using defaults."
            $computerName = "UNKNOWN"
            $action = "PARSE_ERROR"
            $apiSuccess = $false
          }

          Write-Output "=== Configuration Validation ==="
          # Simple validation based on string results
          $configPassed = $env:ConfigJson -eq "CONFIG_LOADED_SUCCESS"
          Write-Output "  Configuration Loading: $(if ($configPassed) { 'PASS' } else { 'FAIL' })"

          Write-Output "=== AD Object Creation Validation ==="
          # Simple validation based on parsed results
          $hasComputerName = -not [string]::IsNullOrEmpty($computerName) -and $computerName -ne "UNKNOWN"
          $hasAction = -not [string]::IsNullOrEmpty($action) -and $action -ne "PARSE_ERROR"
          $hasJobId = -not [string]::IsNullOrEmpty($jobId)
          $hasTimestamp = -not [string]::IsNullOrEmpty($timestamp)
          $hasActualOU = -not [string]::IsNullOrEmpty($actualOU)
          $ouMatches = $targetOU -eq $actualOU

          Write-Output "  Has Computer Name: $(if ($hasComputerName) { 'PASS' } else { 'FAIL' })"
          Write-Output "  Has Action: $(if ($hasAction) { 'PASS' } else { 'FAIL' })"
          Write-Output "  Has Job ID: $(if ($hasJobId) { 'PASS' } else { 'FAIL' })"
          Write-Output "  Has Timestamp: $(if ($hasTimestamp) { 'PASS' } else { 'FAIL' })"
          Write-Output "  API Successful: $(if ($apiSuccess) { 'PASS' } else { 'FAIL' })"
          Write-Output "  Has Actual OU: $(if ($hasActualOU) { 'PASS' } else { 'FAIL' })"
          Write-Output "  OU Matches: $(if ($ouMatches) { 'PASS' } else { 'FAIL' })"

          Write-Output "=== Detailed Results Summary ==="
          Write-Output "Configuration:"
          Write-Output "  Asset Owner: $assetOwner"
          Write-Output "  Client: $client"
          Write-Output "  App Type: $appType"
          Write-Output "  Environment: $environment"
          Write-Output "  OS Version: $osVersion"
          Write-Output "  Domain: $domain"
          Write-Output "  Target OU: $targetOU"

          Write-Output "AD Object Creation:"
          Write-Output "  Computer Name: $computerName"
          Write-Output "  Action Taken: $action"
          Write-Output "  API Success: $apiSuccess"
          Write-Output "  API Message: $apiMessage"
          Write-Output "  Target OU: $targetOU"
          Write-Output "  Actual OU: $actualOU"
          Write-Output "  Job ID: $jobId"
          Write-Output "  Test Mode: $testMode"
          Write-Output "  Timestamp: $timestamp"

          # Overall validation status
          $adPassed = $hasComputerName -and $hasAction -and $hasJobId -and $hasTimestamp -and $hasActualOU
          $overallPassed = $configPassed -and $adPassed

          Write-Output "=== Final Validation Status ==="
          Write-Output "Overall Status: $(if ($overallPassed) { 'PASSED' } else { 'FAILED' })"
          Write-Output "Configuration: $(if ($configPassed) { 'PASSED' } else { 'FAILED' })"
          Write-Output "AD Object Creation: $(if ($adPassed) { 'PASSED' } else { 'FAILED' })"

          if ($overallPassed) {
            Write-Output "All validations passed - Ready for full deployment"
          } else {
            Write-Warning "Some validations failed - Review configuration and AD setup"
          }

          Write-Output "=== Results Validation Completed ==="

          # Return simple validation result string to avoid LinkedHashMap issues
          $validationResult = "VALIDATION_$(if ($overallPassed) { 'PASSED' } else { 'FAILED' })_CONFIG_$(if ($configPassed) { 'PASSED' } else { 'FAILED' })_AD_$(if ($adPassed) { 'PASSED' } else { 'FAILED' })_$(Get-Date -Format 'yyyyMMdd-HHmmss')"
          $validationResult

        } catch {
          Write-Error "Results validation failed: $($_.Exception.Message)"
          throw "Results validation failed: $($_.Exception.Message)"
        }
      InputPayload:
        ConfigJson: "{{loadConfiguration.ConfigurationResult}}"
        ADObjectJson: "{{createADObject.ADObjectResult}}"
    outputs:
      - Name: ValidationResult
        Selector: $.Payload
        Type: String
