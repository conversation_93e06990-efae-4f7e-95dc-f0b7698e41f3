schemaVersion: "0.3"
description: |-
  ULTRA MINIMAL TEST - No functions, just inline PowerShell
  Testing if the issue is with PowerShell functions or something else.
assumeRole: "{{AutomationAssumeRole}}"
parameters:
  AutomationAssumeRole:
    default: ""
    description: (Optional) The ARN of the role that allows Automation to perform the actions on your behalf.
    type: String
  AssetOwner:
    description: (Required) Asset Owner (e.g., Sanlam, Santam, Retail Mass)
    type: String
  Region:
    description: (Optional) AWS Region
    type: String
    default: af-south-1
mainSteps:
  - description: ULTRA MINIMAL TEST - Inline PowerShell without functions
    name: ultraMinimalTest
    action: aws:executeScript
    timeoutSeconds: 300
    isEnd: true
    inputs:
      Runtime: PowerShell 7.4
      Script: |-
        Write-Output "Ultra minimal test started"
        Write-Output "Asset Owner: $env:AssetOwner"
        Write-Output "Region: $env:Region"
        Write-Output "Ultra minimal test completed"
        "INLINE_SUCCESS"
      InputPayload:
        AssetOwner: "{{AssetOwner}}"
        Region: "{{Region}}"
    outputs:
      - Name: TestResult
        Selector: $.Payload
        Type: String
